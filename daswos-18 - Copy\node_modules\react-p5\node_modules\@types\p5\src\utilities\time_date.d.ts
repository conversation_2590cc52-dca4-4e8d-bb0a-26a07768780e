// This file was auto-generated. Please do not edit it.

import * as p5 from '../../index';

declare module '../../index' {
    interface p5InstanceExtensions {
        /**
         *   p5.js communicates with the clock on your
         *   computer. The day() function returns the current
         *   day as a value from 1 - 31.
         *   @return the current day
         */
        day(): number;

        /**
         *   p5.js communicates with the clock on your
         *   computer. The hour() function returns the current
         *   hour as a value from 0 - 23.
         *   @return the current hour
         */
        hour(): number;

        /**
         *   p5.js communicates with the clock on your
         *   computer. The minute() function returns the
         *   current minute as a value from 0 - 59.
         *   @return the current minute
         */
        minute(): number;

        /**
         *   Returns the number of milliseconds (thousandths of
         *   a second) since starting the sketch (when setup()
         *   is called). This information is often used for
         *   timing events and animation sequences.
         *   @return the number of milliseconds since starting
         *   the sketch
         */
        millis(): number;

        /**
         *   p5.js communicates with the clock on your
         *   computer. The month() function returns the current
         *   month as a value from 1 - 12.
         *   @return the current month
         */
        month(): number;

        /**
         *   p5.js communicates with the clock on your
         *   computer. The second() function returns the
         *   current second as a value from 0 - 59.
         *   @return the current second
         */
        second(): number;

        /**
         *   p5.js communicates with the clock on your
         *   computer. The year() function returns the current
         *   year as an integer (2014, 2015, 2016, etc).
         *   @return the current year
         */
        year(): number;
    }
}
