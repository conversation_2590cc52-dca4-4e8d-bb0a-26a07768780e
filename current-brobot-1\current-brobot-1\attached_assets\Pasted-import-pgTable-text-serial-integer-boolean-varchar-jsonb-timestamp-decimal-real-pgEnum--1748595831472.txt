import { pgTable, text, serial, integer, boolean, varchar, jsonb, timestamp, decimal, real, pgEnum, primaryKey, pgSchema } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { never, z } from "zod";
import { relations } from "drizzle-orm";
import { sql } from "drizzle-orm";
import { truncate } from "fs";

// User Schema
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  email: text("email").notNull().unique(),
  fullName: text("full_name").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  isSeller: boolean("is_seller").default(false).notNull(),
  isAdmin: boolean("is_admin").default(false).notNull(),
  avatar: text("avatar"),
  hasSubscription: boolean("has_subscription").default(false).notNull(),
  subscriptionType: text("subscription_type"), // "limited", "unlimited", or legacy types
  subscriptionExpiresAt: timestamp("subscription_expires_at"),
  // Family account fields
  isFamilyOwner: boolean("is_family_owner").default(false).notNull(),
  familyOwnerId: integer("family_owner_id"), // For family member accounts, points to the owner
  parentAccountId: integer("parent_account_id"), // ID of the family owner account (null for family owners and individual accounts)
  isChildAccount: boolean("is_child_account").default(false).notNull(), // Whether this is a child account
  // SuperSafe mode fields
  superSafeMode: boolean("super_safe_mode").default(false).notNull(),
  superSafeSettings: jsonb("super_safe_settings").default({
    blockGambling: true,
    blockAdultContent: true,
    blockOpenSphere: false
  }),
  // SafeSphere mode field
  safeSphereActive: boolean("safe_sphere_active").default(false).notNull(),
  // AI Shopper settings
  aiShopperEnabled: boolean("ai_shopper_enabled").default(false).notNull(),
  aiShopperSettings: jsonb("ai_shopper_settings").default({
    autoPurchase: false,
    autoPaymentEnabled: false,
    confidenceThreshold: 0.85, // Minimum confidence score (0-1) required for auto-purchase
    budgetLimit: 5000, // in cents ($50)
    maxTransactionLimit: 10000, // in cents ($100) - maximum for a single transaction
    preferredCategories: [],
    avoidTags: [],
    minimumTrustScore: 85,
    purchaseMode: "refined", // "refined" or "random"
    maxPricePerItem: 5000, // in cents ($50)

    // DasWos Coins settings
    maxCoinsPerItem: 50,
    maxCoinsPerDay: 100,
    maxCoinsOverall: 1000,

    // Purchase frequency settings
    purchaseFrequency: {
      hourly: 1,
      daily: 5,
      monthly: 50
    },
  }),

  // Identity Verification fields
  identityVerified: boolean("identity_verified").default(false).notNull(),
  identityVerificationStatus: text("identity_verification_status").default("none").notNull(), // "none" or values starting with "app" (e.g., "approved", "app_pending", "application_review")
  identityVerificationSubmittedAt: timestamp("identity_verification_submitted_at"),
  identityVerificationApprovedAt: timestamp("identity_verification_approved_at"),
  identityVerificationData: jsonb("identity_verification_data").default({}), // Store verification details

  // Trust Score (calculated field, but we can cache it here for performance)
  trustScore: integer("trust_score").default(30).notNull(), // Base score of 30 for having an account

  // DasWos Coins Balance (cached from transactions for performance)
  dasWosCoinsBalance: integer("daswos_coins_balance").default(0).notNull(), // Current balance in DasWos coins

  // Seller/Business Information (merged from sellers table)
  businessName: text("business_name"), // Optional business name
  businessType: text("business_type").default("individual").notNull(), // "individual", "business", "corporation", etc.
  businessAddress: text("business_address"), // Business address
  contactPhone: text("contact_phone"), // Contact phone number
  taxId: text("tax_id"), // Tax ID for business accounts
  website: text("website"), // Business website
  yearEstablished: integer("year_established"), // Year business was established
  businessDescription: text("business_description"), // Business description
  profileImageUrl: text("profile_image_url"), // Business profile image
  documentUrls: text("document_urls").array(), // Array of document URLs
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
  email: true,
  fullName: true,
  parentAccountId: true,
  isChildAccount: true,
  superSafeMode: true,
  superSafeSettings: true,
  identityVerified: true,
  identityVerificationStatus: true,
  identityVerificationData: true,
  businessName: true,
  businessType: true,
  businessAddress: true,
  contactPhone: true,
  taxId: true,
  website: true,
  yearEstablished: true,
  businessDescription: true,
  profileImageUrl: true,
  documentUrls: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;

// Categories Schema
export const categories = pgTable("categories", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  parentId: integer("parent_id"), // Self-reference defined in relations
  level: integer("level").notNull().default(0),
  isActive: boolean("is_active").notNull().default(true),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at"),
});

// Category closure table for efficient hierarchical queries
export const categoryClosure = pgTable("category_closure", {
  ancestorId: integer("ancestor_id").notNull().references(() => categories.id),
  descendantId: integer("descendant_id").notNull().references(() => categories.id),
  depth: integer("depth").notNull(),
}, (t) => ({
  pk: primaryKey(t.ancestorId, t.descendantId),
}));

export const insertCategorySchema = createInsertSchema(categories).omit({
  id: true,
  // createdAt: true, // Removed as it is not assignable
  updatedAt: true,
});

export type InsertCategory = z.infer<typeof insertCategorySchema>;
export type Category = typeof categories.$inferSelect;

// Product Schema (Enhanced with vector search and AI attributes)
export const products = pgTable("products", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  description: text("description").notNull(),
  price: integer("price").notNull(), // In cents
  imageUrl: text("image_url").notNull(),
  sellerId: integer("seller_id").notNull(),
  sellerName: text("seller_name").notNull(),
  sellerVerified: boolean("seller_verified").notNull().default(false),
  sellerType: text("seller_type").notNull().default("merchant"), // merchant, personal
  trustScore: integer("trust_score").notNull(),
  // Identity verification fields copied from seller at time of listing
  identityVerified: boolean("identity_verified").notNull().default(false),
  identityVerificationStatus: text("identity_verification_status").notNull().default("none"),
  tags: text("tags").array().notNull(),
  shipping: text("shipping").notNull(),
  originalPrice: integer("original_price"), // In cents, for discounts
  discount: integer("discount"), // Percentage
  verifiedSince: text("verified_since"),
  warning: text("warning"),
  isBulkBuy: boolean("is_bulk_buy").default(false).notNull(), // Whether this product is available for bulk purchase
  bulkMinimumQuantity: integer("bulk_minimum_quantity"), // Minimum quantity for bulk discount
  bulkDiscountRate: integer("bulk_discount_rate"), // Special discount rate for bulk purchases
  imageDescription: text("image_description"), // AI-generated description of the product image

  // New AI-specific fields
  categoryId: integer("category_id").references(() => categories.id),
  aiAttributes: jsonb("ai_attributes").default('{}'),
  // Vector search field for semantic search (handled via SQL directly)
  // We'll implement vector search through direct SQL commands rather than through the ORM
  searchVector: text("search_vector"),

  // Product status and inventory management
  status: text("status").notNull().default("active"), // "draft", "active", "sold", "inactive"
  quantity: integer("quantity").notNull().default(1), // Available quantity
  soldQuantity: integer("sold_quantity").notNull().default(0), // Quantity sold

  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at"),
});

// Relations
export const productsRelations = relations(products, ({ one }) => ({
  seller: one(users, {
    fields: [products.sellerId],
    references: [users.id],
  }),
}));

export const usersRelations = relations(users, ({ many }) => ({
  products: many(products)
}));
