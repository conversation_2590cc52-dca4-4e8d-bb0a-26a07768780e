{"name": "react-p5", "version": "1.4.1", "description": "This Component lets you integrate p5 Sketches into your React App", "main": "./build/index.js", "types": "./@types", "keywords": ["react", "p5", "p5js"], "repository": {"url": "Gherciu/react-p5", "type": "git"}, "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://gherciu.github.io/"}, "collective": {"type": "opencollective", "url": "https://opencollective.com/gherciu-g<PERSON><PERSON><PERSON>e"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/gherciu-g<PERSON><PERSON><PERSON>e"}, "license": "MIT", "scripts": {"build": "rimraf ./build && cross-env NODE_ENV=production webpack", "watch": "cross-env NODE_ENV=development webpack -w", "test": "jest", "postinstall": "opencollective-postinstall || exit 0"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "jest": {"setupFiles": ["jest-canvas-mock"]}, "peerDependencies": {"react": ">= 17.0.1", "react-dom": ">= 17.0.1"}, "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"], "dependencies": {"@types/p5": "1.7.0", "opencollective-postinstall": "2.0.2", "p5": "1.7.0"}, "devDependencies": {"@babel/core": "7.6.2", "@babel/preset-env": "7.6.2", "@babel/preset-react": "7.0.0", "@commitlint/cli": "8.2.0", "@commitlint/config-conventional": "8.2.0", "@testing-library/react": "11.2.5", "babel-loader": "8.0.6", "cross-env": "6.0.3", "husky": "3.0.8", "jest": "26.6.3", "jest-canvas-mock": "2.3.1", "react": "16.10.2", "react-dom": "16.10.2", "webpack": "4.41.0", "webpack-cli": "3.3.9"}}