// This file was auto-generated. Please do not edit it.

import * as p5 from '../../index';

declare module '../../index' {
    class Renderer extends Element {
        /**
         *   Main graphics and rendering context, as well as
         *   the base API implementation for p5.js "core". To
         *   be used as the superclass for Renderer2D and
         *   Renderer3D classes, respectively.
         *
         *   @param elt DOM node that is wrapped
         *   @param [pInst] pointer to p5 instance
         *   @param [isMainCanvas] whether we're using it as
         *   main canvas
         */
        constructor(elt: HTMLElement, pInst?: p5, isMainCanvas?: boolean);
    }
}
