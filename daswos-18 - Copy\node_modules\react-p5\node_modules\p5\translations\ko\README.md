# Welcome to the FES Korean branch!
안녕하세요, FES 한국어 브랜치에 어서오세요!

## 한국어 공동 번역 기여자 Korean Translation Credits
2021년 가을부터 공동작업으로 진행되어 2022년 1월에 마무리된 FES 에러메시지 공동 번역 작업은 아래 분들이 함께하셨습니다.
* [염인화](https://yinhwa.art/) (Inhwa Yeom): artist/XR researcher based in South Korea. (Take a look at her works on [p5 for 50+](https://p5for50.plus/) ([Processing Foundation Fellows 2020](https://medium.com/processing-foundation/p5-js-for-ages-50-in-korea-50d47b5927fb)) and p5js website Korean translation)
* 전유진 (Youjin Jeon): artist/organizer based in Seoul, South Korea. [여성을 위한 열린 기술랩(Woman Open Tech Lab.kr)](http://womanopentechlab.kr/) and [Seoul Express](http://seoulexpress.kr/)
* [정앎](https://www.almichu.com/) (Alm Chung, organizer): Korean-American artist/researcher based in Seattle, WA.
* 이지현 (Ji<PERSON><PERSON> Lee): Korean publishing editor based in South Korea

## 영한 번역 리소스 (Korean-English Translation Resources)
* 영한 [번역에 도움이 되는 툴과 유의점들]입니다.
* 또한 영한 [번역 작업 중 마주치는 딜레마들] 속에서 저희가 채택한 방식을 모아 적어봤습니다.
* 외래 [기술 용어 다루기]에 대한 논의입니다.
* p5js 웹사이트와 기술 문서에서 사용하는 기술 용어들을 통일하기위해 사용하고 있  [p5js.org/ko 기술 용어 색인] 입니다.
* 현존하는 검색툴/번역 툴들과 연계 가능한 "[사이를 맴도는]" 번역문에 대해 생각해보는 글입니다.

이 외에도 FES의 세계화 작업 과정, 그리고 과정 중 논의된 이슈들을 [Friendly Errors i18n Book ✎ 친절한 오류 메시지 세계화 가이드북]에서 읽어보실 수 있습니다. "친절한 오류 메시지 세계화 가이드북"은 오픈 소스 프로젝트이며, 이 [독립된 레파지토리 (repository)]를 통해 기여 가능합니다.


[번역에 도움이 되는 툴과 유의점들]: https://almchung.github.io/p5-fes-i18n-book/ch4/#tools
[번역 작업 중 마주치는 딜레마들]: https://almchung.github.io/p5-fes-i18n-book/ch4/#dilemmas
[기술 용어 다루기]: https://almchung.github.io/p5-fes-i18n-book/ch3/
[사이를 맴도는]: https://almchung.github.io/p5-fes-i18n-book/ch5/
[Friendly Errors i18n Book ✎ 친절한 오류 메시지 세계화 가이드북]: https://almchung.github.io/p5-fes-i18n-book/
[독립된 레파지토리 (repository)]: https://github.com/almchung/p5-fes-i18n-book