import * as p5 from './index';

declare module './index' {
    type ADD = 'lighter';
    type ARROW = 'arrow';
    type AUTO = 'auto';
    type AXES = 'axes';
    type BASELINE = 'alphabetic';
    type BEVEL = 'bevel';
    type BEZIER = 'bezier';
    type BLEND = 'source-over';
    type BLUR = 'blur';
    type BOLD = 'bold';
    type BOLDITALIC = 'bolditalic';
    type BOTTOM = 'bottom';
    type BURN = 'color-burn';
    type CENTER = 'center';
    type CHAR = 'char';
    type CHORD = 'chord';
    type CLAMP = 'clamp';
    type CLOSE = 'close';
    type CONTAIN = 'contain';
    type CORNER = 'corner';
    type CORNERS = 'corners';
    type COVER = 'cover';
    type CROSS = 'cross';
    type CURVE = 'curve';
    type DARKEST = 'darkest';
    type DEGREES = 'degrees';
    type DIFFERENCE = 'difference';
    type DILATE = 'dilate';
    type DODGE = 'color-dodge';
    type ERODE = 'erode';
    type EXCLUSION = 'exclusion';
    type FALLBACK = 'fallback';
    type FILL = 'fill';
    type FLOAT = 'float';
    type GRAY = 'gray';
    type GRID = 'grid';
    type HALF_FLOAT = 'half-float';
    type HAND = 'hand';
    type HARD_LIGHT = 'hard-light';
    type HSB = 'hsb';
    type HSL = 'hsl';
    type IMAGE = 'image';
    type IMMEDIATE = 'immediate';
    type INVERT = 'invert';
    type ITALIC = 'italic';
    type LABEL = 'label';
    type LANDSCAPE = 'landscape';
    type LEFT = 'left';
    type LIGHTEST = 'lighten';
    type LINE_LOOP = 0x0002;
    type LINE_STRIP = 0x0003;
    type LINEAR = 'linear';
    type LINES = 0x0001;
    type MIRROR = 'mirror';
    type MITER = 'miter';
    type MOVE = 'move';
    type MULTIPLY = 'multiply';
    type NEAREST = 'nearest';
    type NORMAL = 'normal';
    type OPAQUE = 'opaque';
    type OPEN = 'open';
    type OVERLAY = 'overlay';
    type P2D = 'p2d';
    type PIE = 'pie';
    type POINTS = 0x0000;
    type PORTRAIT = 'portrait';
    type POSTERIZE = 'posterize';
    type PROJECT = 'square';
    type QUAD_STRIP = 'quad_strip';
    type QUADRATIC = 'quadratic';
    type QUADS = 'quads';
    type RADIANS = 'radians';
    type RADIUS = 'radius';
    type REMOVE = 'destination-out';
    type REPEAT = 'repeat';
    type REPLACE = 'copy';
    type RGB = 'rgb';
    type RGBA = 'rgba';
    type RIGHT = 'right';
    type ROUND = 'round';
    type SCREEN = 'screen';
    type SOFT_LIGHT = 'soft-light';
    type SQUARE = 'butt';
    type STROKE = 'stroke';
    type SUBTRACT = 'subtract';
    type TESS = 'tess';
    type TEXT = 'text';
    type TEXTURE = 'texture';
    type THRESHOLD = 'threshold';
    type TOP = 'top';
    type TRIANGLE_FAN = 0x0006;
    type TRIANGLE_STRIP = 0x0005;
    type TRIANGLES = 0x0004;
    type UNSIGNED_BYTE = 'unsigned-byte';
    type UNSIGNED_INT = 'unsigned-int';
    type VERSION = 'version';
    type WAIT = 'wait';
    type WEBGL = 'webgl';
    type WEBGL2 = 'webgl2';
    type WORD = 'word';
    type VIDEO = 'video';
    type AUDIO = 'audio';
}
