{"fes": {"autoplay": "जिस मीडिया को चलाने की कोशिश की गई ('{{src}}' के साथ) उसे इस ब्राउज़र द्वारा अनुमति नहीं दी गई, संभवतः ब्राउज़र की स्वत: प्ले नीति के कारण।\n\n+ अधिक जानकारी: {{url}}", "checkUserDefinedFns": "ऐसा लगता है कि आपने गलती से {{actualName}} की जगह {{name}} लिख दिया है। यदि यह जानबूझकर नहीं किया गया है तो कृपया इसे सुधारें।", "fileLoadError": {"bytes": "ऐसा लगता है कि आपकी फ़ाइल लोड करने में कोई समस्या थी। {{suggestion}}", "font": "ऐसा लगता है कि आपका मुद्रलिपि लोड करने में कोई समस्या थी। {{suggestion}}", "gif": "आपकी GIF लोड करने में कुछ समस्या हुई। सुनिश्चित करें कि आपका GIF 87a या 89a एन्कोडिंग का उपयोग कर रहा है।", "image": "ऐसा लगता है कि आपकी छवि लोड करने में कोई समस्या थी। {{suggestion}}", "json": "ऐसा लगता है कि आपकी JSON फ़ाइल लोड करने में कोई समस्या थी। {{suggestion}}", "large": "यदि आपकी बड़ी फ़ाइल सफलतापूर्वक नहीं लाई गई है, तो हम फ़ाइल को छोटे खंडों में विभाजित करने और उन्हें लाने की सलाह देते हैं।", "strings": "ऐसा लगता है कि आपकी पाठ फ़ाइल लोड करने में कोई समस्या थी। {{suggestion}}", "suggestion": "यह जाँचने का प्रयास करें कि फ़ाइल पथ ({{filePath}}) सही है या नहीं, फ़ाइल को ऑनलाइन होस्ट करना, या स्थानीय सर्वर चलाना।\n\n+ अधिक जानकारी: {{url}}", "table": "ऐसा लगता है कि आपकी तालिका फ़ाइल लोड करने में कोई समस्या थी। {{suggestion}}", "xml": "ऐसा लगता है कि आपकी XML फ़ाइल लोड करने में कोई समस्या थी। {{suggestion}}"}, "friendlyParamError": {"type_EMPTY_VAR": "{{location}} {{func}}() {{position}} प्राचल के लिए {{formatType}} की अपेक्षा कर रहा था, इसके बजाय एक खाली चर प्राप्त हुआ। यदि जानबूझकर नहीं किया गया है, तो यह अक्सर दायरे के साथ एक समस्या है।\n\n+ अधिक जानकारी:{{url}}", "type_TOO_FEW_ARGUMENTS": "{{location}} {{func}}() कम से कम {{minParams}} तर्कों की अपेक्षा थी, लेकिन केवल प्राप्त हुए {{argCount}}.", "type_TOO_MANY_ARGUMENTS": "{{location}} {{func}}() {{maxParams}} से अधिक तर्कों की अपेक्षा नहीं कर रहा था, लेकिन प्राप्त हुआ {{argCount}}.", "type_WRONG_TYPE": "{{location}} {{func}}() {{position}} प्राचल के लिए {{formatType}} की अपेक्षा कर रहा था, इसके बजाय {{argType}} प्राप्त हुआ।"}, "globalErrors": {"reference": {"cannotAccess": "\n{{location}} \"{{symbol}}\" घोषणा से पहले प्रयोग किया जाता है। सुनिश्चित करें कि आपने चर का उपयोग करने से पहले उसे घोषित कर दिया है।\n\n+ अधिक जानकारी: {{url}}", "notDefined": "\n{{location}} \"{{symbol}}\" वर्तमान दायरे में परिभाषित नहीं है। यदि आपने इसे अपने कोड में परिभाषित किया है, तो आपको इसका दायरा, वर्तनी और अक्षर-आवरण जांचना चाहिए (JavaScript केस-संवेदी है)।\n\n+ अधिक जानकारी: {{url}}"}, "stackSubseq": "└[{{location}}] \n\t {{func}}() में पंक्ति {{line}} से आह्वान किया गया\n", "stackTop": "┌[{{location}}] \n\t {{func}}() में पंक्ति {{line}} पर त्रुटि\n", "syntax": {"badReturnOrYield": "\nवाक्य - विन्यास त्रुटि - रिटर्न किसी फ़ंक्शन के बाहर होता है। सुनिश्चित करें कि आपसे कोई कोष्ठक नहीं छूट रहा है, ताकि रिटर्न किसी फ़ंक्शन के अंदर हो।\n\n+ अधिक जानकारी: {{url}}", "invalidToken": "\nवाक्य - विन्यास त्रुटि - एक ऐसा प्रतीक मिला जिसे जावास्क्रिप्ट नहीं पहचानता या अपने स्थान पर अपेक्षित नहीं था।\n\n+ अधिक जानकारी: {{url}}", "missingInitializer": "\nवाक्य - विन्यास त्रुटि - एक नियत चर घोषित किया गया है लेकिन प्रारंभ नहीं किया गया है। जावास्क्रिप्ट में, एक नियत के लिए प्रारंभकर्ता की आवश्यकता होती है। एक मान उसी कथन में निर्दिष्ट किया जाना चाहिए जिसमें चर घोषित किया गया है। त्रुटि में पंक्ति संख्या की जाँच करें और स्थिरांक चर को एक मान निर्दिष्ट करें।\n\n+ अधिक जानकारी: {{url}}", "redeclaredVariable": "\nवाक्य - विन्यास त्रुटि - \"{{symbol}}\" पुनः घोषित किया जा रहा है। जावास्क्रिप्ट एक चर को एक से अधिक बार घोषित करने की अनुमति नहीं देता है। चर की पुनः घोषणा के लिए त्रुटि वाली पंक्ति संख्या की जाँच करें।\n\n+ अधिक जानकारी: {{url}}", "unexpectedToken": "\nवाक्य - विन्यास त्रुटि - प्रतीक ऐसे स्थान पर मौजूद है जिसकी अपेक्षा नहीं थी।\nआम तौर पर यह टंकण त्रुटि के कारण होता है। किसी भी गुम/अतिरिक्त चीज़ के लिए त्रुटि में पंक्ति नंबर की जाँच करें।\n\n+ अधिक जानकारी: {{url}}"}, "type": {"constAssign": "\n{{location}} एक नियत चर को फिर से असाइन किया जा रहा है। जावास्क्रिप्ट में, किसी स्थिरांक को दोबारा मान निर्दिष्ट करने की अनुमति नहीं है। यदि आप किसी चर के लिए नए मान पुनः निर्दिष्ट करना चाहते हैं, तो सुनिश्चित करें कि इसे var या Let के रूप में घोषित किया गया है।\n\n+ अधिक जानकारी: {{url}}", "notfunc": "\n{{location}} \"{{symbol}}\" फ़ंक्शन के रूप में नहीं बुलाया जा सका।\nवर्तनी, अक्षर-आवरण (जावास्क्रिप्ट केस-संवेदी है) और उसके प्रकार की जाँच करें।\n\n+ अधिक जानकारी: {{url}}", "notfuncObj": "\n{{location}} \"{{symbol}}\" फ़ंक्शन के रूप में नहीं बुलाया जा सका।\nसत्यापित करें कि क्या \"{{obj}}\" में \"{{symbol}}\" है और वर्तनी, अक्षर-आवरण (जावास्क्रिप्ट केस-संवेदी है) और उसके प्रकार की जांच करें।\n\n+ अधिक जानकारी: {{url}}", "readFromNull": "\n{{location}} null की संपत्ति को पढ़ा नहीं जा सकता. जावास्क्रिप्ट में मान null इंगित करता है कि किसी ऑब्जेक्ट का कोई मूल्य नहीं है।\n\n+ अधिक जानकारी: {{url}}", "readFromUndefined": "\n{{location}} undefined की संपत्ति नहीं पढ़ सकता. त्रुटि वाली पंक्ति संख्या की जाँच करें और सुनिश्चित करें कि जो चर संचालित किया जा रहा है वह अपरिभाषित नहीं है।\n\n + अधिक जानकारी: {{url}}"}}, "libraryError": "{{location}} जब {{func}} को आह्वान किया गया तो p5js लाइब्रेरी के अंदर \"{{error}}\" संदेश के साथ एक त्रुटि उत्पन्न हुई। यदि अन्यथा नहीं कहा गया है, तो यह {{func}} को दिए गए तर्कों के साथ एक समस्या हो सकती है।", "location": "[{{file}}, पंक्ति {{line}}]", "misspelling": "{{location}} ऐसा लगता है कि आपने गलती से \"{{actualName}}\" के स्थान पर \"{{name}}\" लिख दिया होगा। यदि आप p5.js से {{type}} का उपयोग करना चाहते हैं तो कृपया इसे सही करके {{actualName}} कर दें।", "misspelling_plural": "{{location}} ऐसा लगता है कि आपने गलती से \"{{name}}\" लिख दिया होगा।\nआपका मतलब निम्नलिखित में से कोई एक हो सकता है: \n{{suggestions}}", "misusedTopLevel": "क्या आपने अभी p5.js के {{symbolName}} {{symbolType}} का उपयोग करने का प्रयास किया? यदि ऐसा है, तो आप इसे अपने रेखा-चित्र के setup() फ़ंक्शन में ले जाना चाह सकते हैं।\n\n+ अधिक जानकारी: {{url}}", "positions": {"p_1": "पहला", "p_10": "दसवां", "p_11": "ग्यारहवें", "p_12": "बारहवें", "p_2": "दूसरा", "p_3": "तीसरा", "p_4": "चौथी", "p_5": "पांचवां", "p_6": "छठा", "p_7": "सातवीं", "p_8": "आठवाँ", "p_9": "नौवां"}, "pre": "\n🌸 p5.js कहता है: {{message}}", "sketchReaderErrors": {"reservedConst": "आपने एक p5.js आरक्षित चर \"{{symbol}}\" का उपयोग किया है, सुनिश्चित करें कि आपने चर नाम को किसी और चीज़ में बदल दिया है।\n\n+ अधिक जानकारी: {{url}}", "reservedFunc": "आपने एक p5.js आरक्षित फ़ंक्शन \"{{symbol}}\" का उपयोग किया है, सुनिश्चित करें कि आपने फ़ंक्शन का नाम किसी और चीज़ में बदल दिया है।\n\n+ अधिक जानकारी: {{url}}"}, "welcome": "स्वागत! यह आपका मित्रवत डिबगर है. मुझे बंद करने के लिए, p5.min.js का उपयोग करें।", "wrongPreload": "{{location}} जब \"{{func}}\" को आह्वान किया गया तो p5js लाइब्रेरी के अंदर \"{{error}}\" संदेश के साथ एक त्रुटि उत्पन्न हुई। यदि अन्यथा नहीं कहा गया है, तो यह \"{{func}}\" को प्रीलोड से आह्वान किए जाने के कारण हो सकता है। प्रीलोड फ़ंक्शन के अंदर लोड आह्वान (लोडछवि, लोडजेएसओएन, लोडमुद्रलिपि, लोडस्ट्रिंग्स इत्यादि) के अलावा कुछ भी नहीं होना चाहिए।"}}